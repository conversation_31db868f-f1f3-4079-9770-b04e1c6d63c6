# PostgreSQL MCP 系统架构详解

## 1. 整体架构及组件通信

### 1.1 架构概览
```
Postman/客户端 → HTTP服务器(FastAPI) → MCP客户端 → MCP服务器 → PostgreSQL数据库
```

### 1.2 三个主要组件

#### 1.2.1 HTTP服务器 (`http_server.py`)
- **技术栈**: FastAPI + uvicorn
- **端口**: 默认8000
- **功能**:
  - 提供REST API接口供外部客户端调用
  - 管理单例MCP客户端实例
  - 处理查询路由决策
  - 提供健康检查和工具列表接口

#### 1.2.2 MCP客户端 (`llm_mcpclient.py`)
- **技术栈**: OpenAI客户端 + MCP客户端会话
- **功能**:
  - 连接到Ollama LLM服务器
  - 连接到MCP服务器获取工具
  - 实现Function Calling机制
  - 管理对话流程和工具调用

#### 1.2.3 MCP服务器 (`pg_server1.py`)
- **技术栈**: FastMCP + psycopg连接池
- **传输协议**: SSE (Server-Sent Events)
- **端口**: 默认8080
- **功能**:
  - 管理PostgreSQL连接池
  - 提供7个专用数据库查询工具
  - 处理SQL查询执行和结果格式化

### 1.3 组件间通信机制

#### HTTP服务器 ↔ MCP客户端
- **通信方式**: 直接函数调用（同进程）
- **数据格式**: Python对象 (QueryResponse)
- **连接管理**: 单例模式，应用启动时初始化

#### MCP客户端 ↔ Ollama LLM
- **通信方式**: HTTP REST API (OpenAI兼容)
- **端点**: `http://*************:11434/v1`
- **数据格式**: JSON (OpenAI Chat Completions格式)
- **功能**: Function Calling支持

#### MCP客户端 ↔ MCP服务器
- **通信方式**: SSE (Server-Sent Events)
- **端点**: `http://localhost:8080/sse`
- **协议**: MCP (Model Context Protocol)
- **数据格式**: JSON-RPC over SSE

#### MCP服务器 ↔ PostgreSQL
- **通信方式**: psycopg异步连接池
- **连接池配置**:
  - 最小连接数: 1
  - 最大连接数: 5
  - 连接超时: 30秒
  - 最大生命周期: 300秒
  - 最大空闲时间: 60秒

## 2. 查询路由决策机制

### 2.1 决策流程
```
用户查询 → is_database_related_query() → 路由决策
    ↓                                        ↓
非数据库查询                              数据库查询
    ↓                                        ↓
call_ollama_directly()                  process_query()
    ↓                                        ↓
直接LLM响应                            MCP工具调用
```

### 2.2 关键词匹配逻辑

#### 数据库相关关键词
```python
db_keywords = [
    # 中文关键词
    "查询", "统计", "分析", "报警", "数据", "记录", "表", "数据库",
    "安全帽", "工作服", "红区", "闯入", "进入", "预警", "监测",
    "成都二办", "格理特公司", "格理特", "成都",
    "今天", "昨天", "明天", "日期", "时间", "月份", "年份",
    "多少", "几个", "几条", "总数", "数量", "统计", "上周", "本周",
    "最新", "最近", "历史", "趋势",
    "地址", "区域", "位置", "地点",
    
    # 英文关键词
    "query", "select", "count", "sum", "avg", "max", "min",
    "alarm", "data", "record", "table", "database",
    "helmet", "workclothes", "intrusion", "invasion",
    
    # 日期格式
    "2025", "2024", "2023", "-07-", "-08-", "-09-",
    
    # SQL相关
    "WHERE", "SELECT", "FROM", "ORDER BY", "LIMIT"
]
```

#### 非数据库关键词（优先级更高）
```python
non_db_keywords = [
    "你好", "hello", "hi", "嗨", "您好",
    "天气", "weather", "温度", "气温",
    "你是谁", "who are you", "介绍", "自我介绍",
    "帮助", "help", "使用方法", "怎么用",
    "谢谢", "thank", "感谢",
    "再见", "bye", "goodbye", "拜拜",
    "测试", "test", "sfsf", "asdf", "123",
    "随便", "无聊", "闲聊"
]
```

### 2.3 决策规则
1. **优先检查非数据库关键词** - 如果匹配，直接返回False
2. **检查数据库关键词** - 如果匹配，返回True
3. **默认策略** - 如果都不匹配，返回False（保守策略）

## 3. 可用的数据库查询工具

### 3.1 工具分类

#### 按区域分类的工具
1. **query_table** - 查询所有区域的报警记录
2. **query_chengdu_area** - 查询成都二办区域的报警记录
3. **query_greate_company** - 查询格理特公司区域的报警记录

#### 按报警类型分类的工具
4. **query_helmet_alarms** - 查询安全帽监测类型的报警记录
5. **query_intrusion_alarms** - 查询红区闯入监测类型的报警记录
6. **query_invasion_alarms** - 查询红区进入预警类型的报警记录
7. **query_workclothes_alarms** - 查询工作服监测类型的报警记录

### 3.2 统一的参数格式
所有工具都使用相同的参数结构：

```python
date: str = Field(
    description="查询日期或日期范围",
    default=""
)
limit: int = Field(
    description="返回记录数限制，默认100行，设为0表示无限制", 
    default=100
)
```

#### 日期参数支持格式
- **单个日期**: `"2025-07-21"` (查询该日期当天)
- **日期范围**: `"2025-07-21 to 2025-07-23"` (查询日期范围)
- **留空**: `""` (查询所有记录)

#### Limit参数使用规则
- **默认值**: 100 (保护性能)
- **无限制**: 0 (仅当用户明确要求时)
- **触发无限制的关键词**:
  - "全部记录"、"所有记录"、"全部数据"
  - "不限制记录"、"不限制数量"、"无限制"
  - "完整统计"、"完整分析"、"全量数据"

### 3.3 返回数据格式

#### 固定返回列
所有工具都返回相同的列结构：
- `alarm_first_time` - 报警首次时间
- `address` - 地址
- `alarm_area_name` - 报警区域名称
- `alarm_type_name` - 报警类型名称

#### 响应数据结构
```json
{
    "success": true,
    "schema": "alarm",
    "table": "alm_alarm",
    "full_table_name": "alarm.alm_alarm",
    "date_range": "2025-07-21",
    "original_date_input": "2025-07-21",
    "conditions": "alarm_first_time >= '2025-07-21' AND alarm_first_time < '2025-07-22'",
    "columns": ["alarm_first_time", "address", "alarm_area_name", "alarm_type_name"],
    "rows": [...],
    "returned_rows": 50,
    "total_matching_rows": 150,
    "execution_time": "0.045s",
    "limit": 100,
    "saved_file": "query_result_20250723_143022.json"
}
```

## 4. 数据库连接池管理

### 4.1 连接池配置
```python
self.pool = AsyncConnectionPool(
    conninfo=url,
    min_size=1,          # 最小连接数
    max_size=5,          # 最大连接数
    open=False,          # 延迟打开
    timeout=30.0,        # 连接超时(秒)
    max_lifetime=300.0,  # 连接最大生命周期(秒)
    max_idle=60.0        # 最大空闲时间(秒)
)
```

### 4.2 连接管理机制

#### 连接池初始化
1. **延迟初始化** - 创建时不立即打开连接
2. **连接测试** - 初始化后执行`SELECT 1`测试连接
3. **状态管理** - 维护`_is_valid`标志和`_last_error`信息

#### 连接获取和释放
```python
async with self.connection_pool.pool.connection() as conn:
    conn.row_factory = dict_row  # 设置返回字典格式
    async with conn.cursor() as cursor:
        await cursor.execute(query, params)
        rows = await cursor.fetchall()
```

### 4.3 连接失败处理

#### 连接失败时的行为
1. **设置无效状态** - `_is_valid = False`
2. **记录错误信息** - 保存到`_last_error`
3. **清理资源** - 关闭现有连接池
4. **抛出异常** - 向上层传递错误信息
5. **密码混淆** - 错误信息中隐藏敏感信息

#### 错误恢复机制
- **自动重连** - 下次查询时会尝试重新建立连接
- **优雅降级** - MCP服务器继续运行，但数据库操作失败
- **错误传播** - 将数据库错误传递给HTTP客户端

#### 资源清理
```python
async def cleanup():
    """清理资源"""
    try:
        await db_pool.close()
        logger.info("已关闭数据库连接")
    except Exception as e:
        logger.error(f"关闭数据库连接时出错: {e}")
```

### 4.4 监控和日志
- **连接状态监控** - 通过`is_valid`属性检查
- **错误日志记录** - 详细记录连接和查询错误
- **性能监控** - 记录查询执行时间
- **健康检查** - HTTP API提供连接状态检查端点

---

*本文档详细说明了PostgreSQL MCP系统的架构设计、组件通信、查询路由机制和数据库连接管理等核心功能。*
