# PostgreSQL MCP 项目深度优化建议

## 🚨 紧急优化点

### 1. **安全漏洞修复**

#### SQL注入风险
当前代码存在潜在的SQL注入风险：
```python
# 当前有风险的代码
conditions += f" AND alarm_first_time >= '{date}'"
```

**修复方案**:
```python
# 安全的参数化查询
async def build_safe_query(self, base_query: str, filters: dict):
    conditions = []
    params = []
    
    if filters.get('date'):
        conditions.append("alarm_first_time >= %s")
        params.append(filters['date'])
    
    if filters.get('area'):
        conditions.append("alarm_area_name = %s")
        params.append(filters['area'])
    
    if conditions:
        query = f"{base_query} WHERE {' AND '.join(conditions)}"
    
    return query, params
```

#### 认证授权缺失
```python
# 添加JWT认证
from fastapi import Depends, HTTPException
from fastapi.security import HTTPBearer
import jwt

security = HTTPBearer()

async def verify_token(token: str = Depends(security)):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        return payload.get("user_id")
    except jwt.InvalidTokenError:
        raise HTTPException(401, "Invalid token")

@app.post("/query")
async def execute_query(
    request: QueryRequest, 
    user_id: str = Depends(verify_token)
):
    # 添加用户权限检查
    pass
```

### 2. **性能瓶颈优化**

#### 数据库查询优化
```sql
-- 添加必要的索引
CREATE INDEX CONCURRENTLY idx_alarm_first_time_area 
ON alarm.alm_alarm(alarm_first_time, alarm_area_name);

CREATE INDEX CONCURRENTLY idx_alarm_type_time 
ON alarm.alm_alarm(alarm_type_name, alarm_first_time);

-- 分区表优化（按时间分区）
CREATE TABLE alarm.alm_alarm_2025_07 PARTITION OF alarm.alm_alarm
FOR VALUES FROM ('2025-07-01') TO ('2025-08-01');
```

#### 连接池动态调整
```python
class AdaptiveConnectionPool:
    def __init__(self):
        self.base_size = 2
        self.max_size = 20
        self.current_load = 0
        
    async def adjust_pool_size(self):
        # 根据负载动态调整连接池大小
        if self.current_load > 0.8:
            new_size = min(self.pool.max_size + 2, self.max_size)
            await self.pool.resize(new_size)
        elif self.current_load < 0.3:
            new_size = max(self.pool.max_size - 1, self.base_size)
            await self.pool.resize(new_size)
```

### 3. **架构改进**

#### 缓存层实现
```python
import redis.asyncio as redis
from typing import Optional
import json
import hashlib

class QueryCache:
    def __init__(self, redis_url: str):
        self.redis = redis.from_url(redis_url)
        self.default_ttl = 300  # 5分钟
    
    def _cache_key(self, query: str, params: dict) -> str:
        content = f"{query}:{json.dumps(params, sort_keys=True)}"
        return f"mcp:query:{hashlib.md5(content.encode()).hexdigest()}"
    
    async def get(self, query: str, params: dict) -> Optional[dict]:
        key = self._cache_key(query, params)
        cached = await self.redis.get(key)
        if cached:
            return json.loads(cached)
        return None
    
    async def set(self, query: str, params: dict, result: dict, ttl: int = None):
        key = self._cache_key(query, params)
        await self.redis.setex(
            key, 
            ttl or self.default_ttl, 
            json.dumps(result, default=str)
        )

# 在查询工具中使用缓存
@mcp.tool(description="带缓存的查询工具")
async def cached_query_table(date: str = "", limit: int = 100):
    cache = QueryCache("redis://localhost:6379")
    cache_key = {"date": date, "limit": limit}
    
    # 尝试从缓存获取
    cached_result = await cache.get("query_table", cache_key)
    if cached_result:
        cached_result["from_cache"] = True
        return format_response(cached_result)
    
    # 执行实际查询
    result = await execute_actual_query(date, limit)
    
    # 缓存结果
    await cache.set("query_table", cache_key, result)
    
    return format_response(result)
```

#### 异步队列处理
```python
import asyncio
from asyncio import Queue
from dataclasses import dataclass
from typing import Callable

@dataclass
class QueryTask:
    query_id: str
    query: str
    callback: Callable
    priority: int = 0

class QueryProcessor:
    def __init__(self, max_workers: int = 5):
        self.queue = Queue()
        self.workers = []
        self.max_workers = max_workers
        self.running = False
    
    async def start(self):
        self.running = True
        for i in range(self.max_workers):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.workers.append(worker)
    
    async def _worker(self, name: str):
        while self.running:
            try:
                task = await self.queue.get()
                logger.info(f"{name} processing {task.query_id}")
                
                # 执行查询
                result = await self._execute_query(task.query)
                await task.callback(result)
                
                self.queue.task_done()
            except Exception as e:
                logger.error(f"{name} error: {e}")
    
    async def submit_query(self, task: QueryTask):
        await self.queue.put(task)
```

## 🔧 功能增强

### 1. **智能查询建议**
```python
class QuerySuggestionEngine:
    def __init__(self):
        self.common_patterns = {
            "时间查询": ["今天", "昨天", "本周", "上月"],
            "区域查询": ["成都二办", "格理特公司"],
            "类型查询": ["安全帽", "工作服", "红区闯入"]
        }
    
    def suggest_improvements(self, query: str) -> List[str]:
        suggestions = []
        
        # 检查是否包含时间限制
        if not any(pattern in query for pattern in self.common_patterns["时间查询"]):
            suggestions.append("建议添加时间范围，如'今天'或'本周'")
        
        # 检查是否指定了区域
        if not any(pattern in query for pattern in self.common_patterns["区域查询"]):
            suggestions.append("建议指定查询区域，如'成都二办'")
        
        return suggestions

# 在查询响应中添加建议
async def execute_query_with_suggestions(request: QueryRequest):
    suggestion_engine = QuerySuggestionEngine()
    suggestions = suggestion_engine.suggest_improvements(request.query)
    
    result = await execute_query(request)
    
    if suggestions:
        result.response += f"\n\n💡 查询建议：\n" + "\n".join(f"• {s}" for s in suggestions)
    
    return result
```

### 2. **实时监控和告警**
```python
from prometheus_client import Counter, Histogram, Gauge
import time

# 定义监控指标
query_counter = Counter('mcp_queries_total', 'Total queries', ['type', 'status'])
query_duration = Histogram('mcp_query_duration_seconds', 'Query duration')
active_connections = Gauge('mcp_active_connections', 'Active DB connections')
error_rate = Counter('mcp_errors_total', 'Total errors', ['error_type'])

class MonitoringMiddleware:
    def __init__(self):
        self.start_time = time.time()
    
    async def __call__(self, request, call_next):
        start_time = time.time()
        
        try:
            response = await call_next(request)
            
            # 记录成功指标
            query_counter.labels(type='success', status='200').inc()
            query_duration.observe(time.time() - start_time)
            
            return response
            
        except Exception as e:
            # 记录错误指标
            error_rate.labels(error_type=type(e).__name__).inc()
            query_counter.labels(type='error', status='500').inc()
            raise

# 添加告警规则
class AlertManager:
    def __init__(self):
        self.thresholds = {
            'error_rate': 0.05,  # 5%错误率
            'response_time': 5.0,  # 5秒响应时间
            'connection_usage': 0.8  # 80%连接池使用率
        }
    
    async def check_alerts(self):
        # 检查错误率
        current_error_rate = self.calculate_error_rate()
        if current_error_rate > self.thresholds['error_rate']:
            await self.send_alert(f"错误率过高: {current_error_rate:.2%}")
        
        # 检查响应时间
        avg_response_time = self.calculate_avg_response_time()
        if avg_response_time > self.thresholds['response_time']:
            await self.send_alert(f"响应时间过长: {avg_response_time:.2f}s")
    
    async def send_alert(self, message: str):
        # 发送到Slack/邮件/钉钉等
        logger.critical(f"ALERT: {message}")
```

### 3. **配置热更新**
```python
import asyncio
import yaml
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class ConfigWatcher(FileSystemEventHandler):
    def __init__(self, config_manager):
        self.config_manager = config_manager
    
    def on_modified(self, event):
        if event.src_path.endswith('config.yaml'):
            asyncio.create_task(self.config_manager.reload_config())

class ConfigManager:
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config = {}
        self.callbacks = []
    
    async def reload_config(self):
        try:
            with open(self.config_path, 'r') as f:
                new_config = yaml.safe_load(f)
            
            # 比较配置变化
            changes = self._detect_changes(self.config, new_config)
            
            if changes:
                logger.info(f"配置已更新: {changes}")
                self.config = new_config
                
                # 通知所有监听器
                for callback in self.callbacks:
                    await callback(changes)
                    
        except Exception as e:
            logger.error(f"配置重载失败: {e}")
    
    def _detect_changes(self, old_config, new_config):
        changes = {}
        for key, value in new_config.items():
            if key not in old_config or old_config[key] != value:
                changes[key] = {'old': old_config.get(key), 'new': value}
        return changes
```

## 🧪 测试和质量保证

### 1. **压力测试**
```python
import asyncio
import aiohttp
import time
from concurrent.futures import ThreadPoolExecutor

class LoadTester:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.results = []
    
    async def run_load_test(self, concurrent_users: int, duration: int):
        """运行负载测试"""
        start_time = time.time()
        tasks = []
        
        for i in range(concurrent_users):
            task = asyncio.create_task(
                self._user_simulation(i, start_time + duration)
            )
            tasks.append(task)
        
        await asyncio.gather(*tasks)
        
        # 分析结果
        self._analyze_results()
    
    async def _user_simulation(self, user_id: int, end_time: float):
        """模拟单个用户行为"""
        async with aiohttp.ClientSession() as session:
            while time.time() < end_time:
                start = time.time()
                try:
                    async with session.post(
                        f"{self.base_url}/query",
                        json={"query": f"查询测试{user_id}"}
                    ) as response:
                        await response.json()
                        
                    self.results.append({
                        'user_id': user_id,
                        'response_time': time.time() - start,
                        'status': response.status,
                        'success': response.status == 200
                    })
                    
                except Exception as e:
                    self.results.append({
                        'user_id': user_id,
                        'response_time': time.time() - start,
                        'status': 0,
                        'success': False,
                        'error': str(e)
                    })
                
                # 模拟用户思考时间
                await asyncio.sleep(1)
    
    def _analyze_results(self):
        """分析测试结果"""
        total_requests = len(self.results)
        successful_requests = sum(1 for r in self.results if r['success'])
        
        response_times = [r['response_time'] for r in self.results if r['success']]
        
        print(f"总请求数: {total_requests}")
        print(f"成功请求数: {successful_requests}")
        print(f"成功率: {successful_requests/total_requests:.2%}")
        print(f"平均响应时间: {sum(response_times)/len(response_times):.3f}s")
        print(f"95%响应时间: {sorted(response_times)[int(len(response_times)*0.95)]:.3f}s")

# 运行测试
async def main():
    tester = LoadTester("http://localhost:8000")
    await tester.run_load_test(concurrent_users=50, duration=60)

if __name__ == "__main__":
    asyncio.run(main())
```

### 2. **自动化测试**
```python
import pytest
from unittest.mock import AsyncMock, patch

class TestQueryExecution:
    @pytest.fixture
    async def mock_mcp_client(self):
        client = AsyncMock()
        client.is_connected = True
        return client
    
    @pytest.mark.asyncio
    async def test_database_query_routing(self, mock_mcp_client):
        """测试数据库查询路由"""
        with patch('http_server.get_mcp_client', return_value=mock_mcp_client):
            # 测试数据库相关查询
            assert is_database_related_query("查询今天的报警") == True
            assert is_database_related_query("你好") == False
    
    @pytest.mark.asyncio
    async def test_query_caching(self):
        """测试查询缓存"""
        cache = QueryCache("redis://localhost:6379")
        
        # 第一次查询
        result1 = await cached_query_table("2025-07-23", 100)
        
        # 第二次查询应该从缓存返回
        result2 = await cached_query_table("2025-07-23", 100)
        
        assert result2.get("from_cache") == True
    
    @pytest.mark.integration
    async def test_end_to_end_query(self):
        """端到端测试"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post("/query", json={
                "query": "查询今天成都二办的安全帽报警"
            })
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] == True
            assert len(data["tool_calls"]) > 0
```

## 📊 部署和运维优化

### 1. **容器化和编排**
```yaml
# docker-compose.production.yml
version: '3.8'
services:
  mcp-http:
    build: .
    replicas: 3
    environment:
      - DATABASE_URL=************************************/db
      - REDIS_URL=redis://redis:6379
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
  
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - mcp-http
  
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: mcp_db
      POSTGRES_USER: mcp_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
  
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
  
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
  
  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana

volumes:
  postgres_data:
  redis_data:
  grafana_data:
```

### 2. **CI/CD流水线**
```yaml
# .github/workflows/deploy.yml
name: Deploy MCP Application

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          pip install pytest pytest-asyncio
      - name: Run tests
        run: pytest tests/ -v
      - name: Run security scan
        run: bandit -r . -f json -o security-report.json
  
  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to production
        run: |
          docker-compose -f docker-compose.production.yml up -d
          docker-compose exec mcp-http python -m pytest tests/integration/
```

这些优化建议涵盖了安全性、性能、可维护性、监控和部署等各个方面，可以显著提升系统的稳定性和用户体验。建议按优先级逐步实施。
