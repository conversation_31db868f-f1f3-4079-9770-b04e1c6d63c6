# MCP (Model Context Protocol) 客户端实现
# 该客户端连接到MCP服务器，获取可用工具，并通过Ollama LLM进行function calling

import asyncio  # 异步编程支持
import json     # JSON数据处理
import logging  # 日志记录
import sys      # 系统相关功能，用于获取命令行参数
import time     # 时间相关功能
from typing import Optional, Dict, List, Any  # 类型注解
from contextlib import AsyncExitStack  # 异步上下文管理器，用于资源清理
from dataclasses import dataclass
from openai import OpenAI  # OpenAI客户端，兼容Ollama API
from mcp import ClientSession  # MCP客户端会话
from mcp.client.sse import sse_client  # MCP SSE(Server-Sent Events)客户端

# 设置日志
logger = logging.getLogger(__name__)


@dataclass
class QueryResponse:
    """查询响应数据结构"""
    success: bool
    query: str
    response: str
    tool_calls: List[Dict[str, Any]]
    execution_time: float
    error: Optional[str] = None


@dataclass
class MCPConfig:
    """MCP客户端配置"""
    openai_api_key: str = "ollama"
    base_url: str = "http://192.168.21.14:11434/v1"
    model: str = "qwen2.5:7b"
    mcp_server_url: Optional[str] = None


class MCPClient:
    def __init__(self, config: Optional[MCPConfig] = None):
        """
        初始化 MCP 客户端

        Args:
            config: MCP客户端配置，如果为None则使用默认配置

        设置连接参数：
        - Ollama API 配置（兼容 OpenAI API 格式）
        - MCP 会话管理
        - 资源清理管理器
        """
        # 使用提供的配置或默认配置
        self.config = config or MCPConfig()

        # 异步资源管理器，用于自动清理连接等资源
        self.exit_stack = AsyncExitStack()

        # 初始化 OpenAI 客户端（实际连接到 Ollama）
        self.client = OpenAI(
            api_key=self.config.openai_api_key,
            base_url=self.config.base_url
        )

        # MCP 会话对象，初始化为 None，连接后才会设置
        self.session: Optional[ClientSession] = None

        # 连接状态管理
        self.is_connected = False
        self.server_url: Optional[str] = None

    async def connect_to_server(self, sse_url: str, verbose: bool = True):
        """
        连接到 MCP SSE 服务器并列出可用工具

        Args:
            sse_url: MCP 服务器的 SSE (Server-Sent Events) 端点 URL
                    例如: "http://localhost:8080/sse"
            verbose: 是否打印连接信息，默认True

        功能说明:
        1. 建立 SSE 连接到 MCP 服务器
        2. 创建 MCP 客户端会话
        3. 初始化会话（握手过程）
        4. 获取并显示服务器提供的所有工具
        """
        try:
            # 建立 SSE 传输连接
            # SSE 是一种服务器向客户端推送数据的技术，MCP 使用它进行双向通信
            sse_transport = await self.exit_stack.enter_async_context(
                sse_client(sse_url)
            )

            # 获取读写流，用于与服务器通信
            self.read, self.write = sse_transport

            # 创建 MCP 客户端会话，管理与服务器的所有交互
            self.session = await self.exit_stack.enter_async_context(
                ClientSession(self.read, self.write)
            )

            # 执行 MCP 协议的初始化握手
            await self.session.initialize()

            # 从 MCP 服务器获取可用工具列表
            response = await self.session.list_tools()
            tools = response.tools

            # 更新连接状态
            self.is_connected = True
            self.server_url = sse_url

            # 显示连接成功信息和可用工具
            if verbose:
                print(f"\n已连接到 SSE 服务器 ({sse_url})，支持以下工具:")
                for tool in tools:
                    # 安全地处理可能为 None 的描述
                    description = tool.description.strip() if tool.description else "无描述"
                    print(f"  - {tool.name}: {description}")

        except Exception as e:
            self.is_connected = False
            self.server_url = None
            raise Exception(f"连接到MCP服务器失败: {str(e)}")

    async def get_available_tools(self) -> List[Dict[str, Any]]:
        """获取可用工具列表"""
        if not self.session:
            raise Exception("MCP会话未初始化，请先连接到服务器")

        response = await self.session.list_tools()
        return [{
            "name": tool.name,
            "description": tool.description,
            "parameters": tool.inputSchema
        } for tool in response.tools]

    async def process_query(self, query: str, return_detailed: bool = False) -> QueryResponse:
        """
        使用 Ollama 处理查询并调用可用的 MCP 工具 (Function Calling)

        Args:
            query: 用户输入的查询文本
            return_detailed: 是否返回详细信息（保留兼容性）

        Returns:
            QueryResponse: 包含详细响应信息的对象

        工作流程:
        1. 构建对话消息
        2. 获取 MCP 工具列表并转换为 OpenAI Function Calling 格式
        3. 调用 Ollama API 进行推理
        4. 如果需要使用工具，则调用 MCP 工具并获取结果
        5. 将工具结果返回给 Ollama 生成最终响应
        """
        start_time = time.time()
        tool_calls_info = []
        # 初始化对话消息列表，包含用户查询
        messages = [{"role": "user", "content": query}]
        
        # 检查 session 是否已初始化
        if not self.session:
            return QueryResponse(
                success=False,
                query=query,
                response="",
                tool_calls=[],
                execution_time=time.time() - start_time,
                error="MCP 会话未初始化，请先连接到服务器"
            )
        
        # 获取 MCP 服务器提供的工具列表
        response = await self.session.list_tools()
        
        # 将 MCP 工具格式转换为 OpenAI Function Calling 格式
        # 这样 Ollama 就能理解并调用这些工具
        available_tools = [{
            "type": "function",  # OpenAI 标准格式要求
            "function": {
                "name": tool.name,  # 工具名称
                "description": tool.description,  # 工具描述
                "parameters": tool.inputSchema  # 工具参数 schema（JSON Schema 格式）
            }
        } for tool in response.tools]

        try:
            # 首先尝试使用工具调用
            try:
                llm_response = self.client.chat.completions.create(
                    model=self.config.model,  # 使用配置的模型
                    messages=messages,  # 对话历史
                    tools=available_tools  # 可用工具列表
                )
            except Exception as tool_error:
                # 如果模型不支持工具调用，回退到普通对话模式
                if "does not support tools" in str(tool_error):
                    # 构建包含工具信息的提示词
                    tool_descriptions = []
                    for tool in available_tools:
                        func = tool["function"]
                        tool_descriptions.append(f"- {func['name']}: {func['description']}")

                    enhanced_prompt = f"""你是一个数据库查询助手。用户的查询是: {query}

可用的数据库工具:
{chr(10).join(tool_descriptions)}

【重要工具使用说明】：
现在有7个查询工具可用：

按区域分类的工具：
1. query_table - 查询所有区域的报警记录
2. query_chengdu_area - 查询成都二办区域的报警记录
3. query_greate_company - 查询格理特公司区域的报警记录

按报警类型分类的工具：
4. query_helmet_alarms - 查询安全帽监测类型的报警记录
5. query_intrusion_alarms - 查询红区闯入监测类型的报警记录
6. query_invasion_alarms - 查询红区进入预警类型的报警记录
7. query_workclothes_alarms - 查询工作服监测类型的报警记录

所有工具的参数格式相同：
- 参数：date (支持两种格式)
  * 单个日期: "2025-07-21" (查询该日期当天)
  * 日期范围: "2025-07-21 to 2025-07-23" (查询从7月21日到7月23日)
  * 留空: "" (查询所有记录)
- 参数：limit (重要！根据用户需求智能设置)
  * 默认: 100 (正常查询)
  * 无限制: 0 (仅当用户明确要求时)

【LIMIT参数使用规则】：
- 默认情况：limit=100 (保护性能)
- 仅当用户明确使用以下表述时，才设置limit=0：
  * "全部记录"、"所有记录"、"全部数据"
  * "不限制记录"、"不限制数量"、"无限制"
  * "完整统计"、"完整分析"、"全量数据"
  * "总结今天的报警使用全部记录"
- 其他情况一律使用limit=100

所有工具都：
- 固定返回列：alarm_first_time, address, alarm_area_name, alarm_type_name
- 按时间倒序排列（最新在前）
- 自动处理日期范围查询

使用示例：
- 正常查询：{"date": "2025-07-21", "limit": 100}
- 全部记录：{"date": "2025-07-21", "limit": 0}
- 日期范围：{"date": "2025-07-21 to 2025-07-23", "limit": 100}

工具选择指南：
- 用户问"安全帽"相关 → query_helmet_alarms
- 用户问"红区闯入"相关 → query_intrusion_alarms
- 用户问"红区进入预警"相关 → query_invasion_alarms
- 用户问"工作服"相关 → query_workclothes_alarms
- 用户问"成都二办"相关 → query_chengdu_area
- 用户问"格理特公司"相关 → query_greate_company
- 用户问"所有"或不指定 → query_table

请分析用户的查询需求，如果需要查询数据库，请明确说明需要使用哪个工具以及具体的参数。
如果不需要查询数据库，请直接回答用户的问题。"""

                    # 使用普通对话模式
                    llm_response = self.client.chat.completions.create(
                        model=self.config.model,
                        messages=[{"role": "user", "content": enhanced_prompt}]
                    )

                    # 返回普通响应
                    response_content = llm_response.choices[0].message.content or "无响应内容"
                    return QueryResponse(
                        success=True,
                        query=query,
                        response=f"[注意: 当前模型 {self.config.model} 不支持工具调用，以下是基于提示词的响应]\n\n{response_content}",
                        tool_calls=[],
                        execution_time=time.time() - start_time
                    )
                else:
                    # 其他错误，重新抛出
                    raise tool_error

            # 处理 Ollama 的响应
            choice = llm_response.choices[0]

            # 检查是否需要调用工具
            if choice.finish_reason == "tool_calls" and choice.message.tool_calls:
                # Ollama 决定需要使用工具
                tool_call = choice.message.tool_calls[0]  # 获取第一个工具调用
                tool_name = tool_call.function.name  # 工具名称
                tool_args = json.loads(tool_call.function.arguments)  # 解析工具参数

                # 记录工具调用信息
                tool_call_info = {
                    "tool_name": tool_name,
                    "parameters": tool_args,
                    "result": None
                }

                # 通过 MCP 执行工具调用
                result = await self.session.call_tool(tool_name, tool_args)
                
                # 将 LLM 的消息（包含工具调用）添加到历史中
                messages.append(choice.message.model_dump())
                
                # 提取工具执行结果的文本内容
                # 处理可能的不同内容类型
                tool_result_content = ""
                if result.content and len(result.content) > 0:
                    first_content = result.content[0]
                    if hasattr(first_content, 'text'):
                        tool_result_content = first_content.text
                    else:
                        tool_result_content = str(first_content)

                # 更新工具调用信息
                tool_call_info["result"] = tool_result_content
                tool_calls_info.append(tool_call_info)

                # 将 LLM 的消息（包含工具调用）添加到历史中
                messages.append(choice.message.model_dump())

                # 将工具执行结果添加到对话历史
                messages.append({
                    "role": "tool",  # 表示这是工具的响应
                    "content": tool_result_content,  # 工具返回的内容
                    "tool_call_id": tool_call.id,  # 关联到对应的工具调用
                })

                # 将包含工具结果的完整对话发送给 Ollama，生成最终响应
                final_response = self.client.chat.completions.create(
                    model=self.config.model,
                    messages=messages,  # 包含工具调用和结果的完整对话
                )

                # 返回详细响应
                final_content = final_response.choices[0].message.content or "无响应内容"
                return QueryResponse(
                    success=True,
                    query=query,
                    response=final_content,
                    tool_calls=tool_calls_info,
                    execution_time=time.time() - start_time
                )

            # 如果不需要工具调用，直接返回 LLM 的响应
            response_content = choice.message.content or "无响应内容"
            return QueryResponse(
                success=True,
                query=query,
                response=response_content,
                tool_calls=tool_calls_info,
                execution_time=time.time() - start_time
            )
            
        except Exception as e:
            # 捕获并返回错误信息
            return QueryResponse(
                success=False,
                query=query,
                response="",
                tool_calls=tool_calls_info,
                execution_time=time.time() - start_time,
                error=f"调用 Ollama 时出错: {str(e)}"
            )

    async def call_ollama_directly(self, query: str) -> QueryResponse:
        """直接调用Ollama，不使用MCP工具"""
        start_time = time.time()

        try:
            # 构建简单的提示词（不包含工具信息）
            simple_messages = [
                {
                    "role": "user",
                    "content": f"你是一个友好的AI助手。请直接回答用户的问题：{query}"
                }
            ]

            # 调用Ollama（不使用工具）
            response = self.client.chat.completions.create(
                model=self.config.model,
                messages=simple_messages
            )

            # 提取响应内容
            choice = response.choices[0]
            response_content = choice.message.content or "无响应内容"

            return QueryResponse(
                success=True,
                query=query,
                response=response_content,
                tool_calls=[],  # 不使用工具
                execution_time=time.time() - start_time
            )

        except Exception as e:
            logger.error(f"直接调用Ollama失败: {e}")
            return QueryResponse(
                success=False,
                query=query,
                response="抱歉，我现在无法回答您的问题。",
                tool_calls=[],
                execution_time=time.time() - start_time,
                error=str(e)
            )

    async def process_query_simple(self, query: str) -> str:
        """
        简化版查询处理，保持与原有接口的兼容性

        Args:
            query: 用户输入的查询文本

        Returns:
            str: 处理后的响应文本
        """
        result = await self.process_query(query)
        if result.success:
            return result.response
        else:
            return result.error or "查询失败"

    async def chat_loop(self):
        """
        运行交互式聊天循环
        
        功能说明:
        1. 提供用户友好的命令行界面
        2. 持续接收用户输入
        3. 调用 process_query 处理每个查询
        4. 显示 Ollama 的响应
        5. 处理用户退出和异常情况
        
        用户可以输入 'quit' 退出程序
        支持 Ctrl+C 快捷键退出
        """
        print(f"\n🤖 MCP 客户端已启动！使用模型: {self.config.model}")
        print("输入 'quit' 退出")
        
        while True:
            try:
                # 获取用户输入，去除首尾空格
                query = input("\n你: ").strip()
                
                # 检查退出命令
                if query.lower() == 'quit':
                    break
                    
                # 如果输入为空，跳过处理
                if not query:
                    continue
                    
                print("\n🤖 思考中...")
                
                # 处理用户查询
                response = await self.process_query_simple(query)

                # 显示 Ollama 的响应
                print(f"\n🤖 Ollama: {response}")
                
            except KeyboardInterrupt:
                # 用户按 Ctrl+C 退出
                print("\n\n👋 再见！")
                break
            except Exception as e:
                # 处理其他异常，但不退出程序
                print(f"\n⚠️ 发生错误: {str(e)}")

    async def cleanup(self):
        """
        清理资源
        
        功能说明:
        - 关闭所有异步资源（MCP 连接、SSE 连接等）
        - 确保程序正确退出，避免资源泄漏
        - 由 AsyncExitStack 自动管理所有已注册的资源
        """
        await self.exit_stack.aclose()


async def main():
    """
    主函数 - 程序入口点
    
    功能说明:
    1. 检查命令行参数（需要提供 MCP 服务器的 SSE URL）
    2. 创建 MCP 客户端实例
    3. 连接到 MCP 服务器
    4. 启动交互式聊天循环
    5. 确保程序退出时正确清理资源
    
    命令行参数:
        sse_url: MCP 服务器的 SSE 端点 URL
                例如: http://localhost:9009/sse
    
    使用示例:
        python llm_mcpclient.py http://localhost:9009/sse
    """
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("Usage: python client.py <sse_url>")
        print("Example: python client.py http://localhost:9009/sse")
        print("\n说明:")
        print("  sse_url: MCP 服务器的 SSE (Server-Sent Events) 端点地址")
        print("  该地址通常由 MCP 服务器提供，用于建立实时通信连接")
        sys.exit(1)

    # 创建 MCP 客户端实例
    client = MCPClient()
    
    try:
        # 连接到指定的 MCP 服务器
        await client.connect_to_server(sys.argv[1])
        
        # 启动交互式聊天循环
        await client.chat_loop()
        
    finally:
        # 无论程序如何退出，都要清理资源
        # 这确保了 SSE 连接、MCP 会话等资源被正确释放
        await client.cleanup()


if __name__ == "__main__":
    """
    程序入口点
    
    当脚本直接运行时（而不是作为模块导入），执行 main() 函数
    使用 asyncio.run() 来运行异步主函数
    """
    asyncio.run(main())