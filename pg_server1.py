#!/usr/bin/env python3
"""
简化版PostgreSQL MCP Server
提供基本的数据库查询和分析功能
"""

import argparse
import asyncio
import json
import logging
import os
import platform
import re
import signal
import sys
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlparse, urlunparse
from datetime import datetime
from pathlib import Path

import mcp.types as types
from mcp.server.fastmcp import FastMCP
from pydantic import Field
from psycopg.rows import dict_row
from psycopg_pool import AsyncConnectionPool

# 设置Windows系统的事件循环策略（必须在任何异步操作之前设置）
if platform.system() == "Windows":
    from asyncio import WindowsSelectorEventLoopPolicy
    asyncio.set_event_loop_policy(WindowsSelectorEventLoopPolicy())
  
# 初始化FastMCP实例
mcp = FastMCP("pg-simple-server")

# 响应类型定义
ResponseType = List[types.TextContent | types.ImageContent | types.EmbeddedResource]

# 日志配置
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# # 添加结果保存目录配置
# RESULTS_DIR = Path("mcp_results")
# RESULTS_DIR.mkdir(exist_ok=True)

# def save_query_result(tool_name: str, params: dict, result_data: dict):
#     """保存MCP工具调用结果到本地JSON文件"""
#     timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
#     filename = f"{tool_name}_{timestamp}.json"
#     filepath = RESULTS_DIR / filename
    
#     # 构建完整的结果数据
#     full_result = {
#         "metadata": {
#             "tool_name": tool_name,
#             "timestamp": datetime.now().isoformat(),
#             "parameters": params,
#             "filename": filename
#         },
#         "data": result_data
#     }
    
#     # 保存到文件
#     with open(filepath, 'w', encoding='utf-8') as f:
#         json.dump(full_result, f, ensure_ascii=False, indent=2, default=str)
    
#     logger.info(f"结果已保存到: {filepath}")
#     return filename


def obfuscate_password(text: str | None) -> str | None:
    """混淆密码信息
    输入: postgresql://user:mypassword@localhost:5432/mydb
    输出: postgresql://user:****@localhost:5432/mydb
    """
    if not text:
        return text
    
    # 处理URL格式的密码
    try:
        parsed = urlparse(text)
        if parsed.scheme and parsed.netloc and parsed.password:
            netloc = parsed.netloc.replace(parsed.password, "****")
            return urlunparse(parsed._replace(netloc=netloc))
    except Exception:
        pass
    
    # 处理字符串中的密码
    password_pattern = re.compile(r'(password\s*=\s*[\'"]?)([^\'"\s&;]+)([\'"]?)', re.IGNORECASE)
    return re.sub(password_pattern, r'\1****\3', text)


class DbConnectionPool:
    """数据库连接池管理"""
    
    def __init__(self, connection_url: Optional[str] = None):
        self.connection_url = connection_url
        self.pool: Optional[AsyncConnectionPool] = None
        self._is_valid = False
        self._last_error = None
    
    async def connect(self, connection_url: Optional[str] = None) -> AsyncConnectionPool:
        """初始化连接池"""
        if self.pool and self._is_valid:
            return self.pool
        
        url = connection_url or self.connection_url
        self.connection_url = url
        
        if not url:
            self._is_valid = False
            self._last_error = "数据库连接URL未提供"
            raise ValueError(self._last_error)
        
        await self.close()
        
        try:
            self.pool = AsyncConnectionPool(
                conninfo=url,
                min_size=1,
                max_size=5,
                open=False,
                timeout=30.0,
                max_lifetime=300.0,
                max_idle=60.0
            )
            
            await self.pool.open()
            
            # 测试连接
            async with self.pool.connection() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT 1")
            
            self._is_valid = True
            self._last_error = None
            return self.pool
            
        except Exception as e:
            self._is_valid = False
            self._last_error = str(e)
            await self.close()
            raise ValueError(f"数据库连接失败: {obfuscate_password(str(e))}") from e
    
    async def close(self):
        """关闭连接池"""
        if self.pool:
            try:
                await self.pool.close()
            except Exception as e:
                logger.warning(f"关闭连接池时出错: {e}")
            finally:
                self.pool = None
                self._is_valid = False
    
    @property
    def is_valid(self) -> bool:
        """检查连接池是否有效"""
        return self._is_valid
    
    @property
    def last_error(self) -> Optional[str]:
        """获取最后的错误信息"""
        return self._last_error


@dataclass
class QueryResult:
    """查询结果数据类"""
    rows: List[Dict[str, Any]]     # 查询结果行数据
    columns: List[str]             # 列名列表
    row_count: int                 # 返回的行数
    execution_time: float          # 查询执行时间(秒)


class SimpleSqlDriver:
    """
    简化的SQL驱动类
    数据库查询执行器，它充当应用层和数据库连接池之间的中间层
    """
    
    def __init__(self, connection_pool: DbConnectionPool):
        self.connection_pool = connection_pool
    
    async def execute_query(self, query: str, params: Optional[List[Any]] = None) -> QueryResult:
        """执行SQL查询"""
        import time
        start_time = time.time()
        # 1. 检查连接池状态
        if not self.connection_pool.is_valid:
            raise ValueError("数据库连接无效")
        # 2. 从连接池获取连接 (自动管理)
        async with self.connection_pool.pool.connection() as conn:
            # 3. 设置行工厂为字典格式
            conn.row_factory = dict_row
            # 4. 获取游标
            async with conn.cursor() as cursor:
                # 5. 执行查询
                if params:
                    await cursor.execute(query, params)
                else:
                    await cursor.execute(query)
                # 6. 获取结果
                rows = await cursor.fetchall()
                columns = [desc.name for desc in cursor.description] if cursor.description else []
                
                execution_time = time.time() - start_time
                
                return QueryResult(
                    rows=rows,
                    columns=columns,
                    row_count=len(rows),
                    execution_time=execution_time
                )


# 全局变量
db_pool = DbConnectionPool()
sql_driver = None


async def get_sql_driver() -> SimpleSqlDriver:
    """获取SQL驱动实例"""
    global sql_driver
    if sql_driver is None:
        sql_driver = SimpleSqlDriver(db_pool)
    return sql_driver


def format_response(data: Any, response_type: str = "json") -> ResponseType:
    """格式化响应数据"""
    if response_type == "json":
        content = json.dumps(data, indent=2, ensure_ascii=False, default=str)
    elif response_type == "table" and isinstance(data, dict) and "rows" in data:
        content = format_table(data)
    else:
        content = str(data)
    
    return [types.TextContent(type="text", text=content)]


def format_table(data: dict) -> str:
    """格式化表格数据"""
    if not data.get("rows"):
        return "无数据"
    
    rows = data["rows"]
    if not rows:
        return "无数据"
    
    # 获取列名
    columns = list(rows[0].keys())
    
    # 计算列宽
    col_widths = {}
    for col in columns:
        col_widths[col] = max(len(str(col)), max(len(str(row.get(col, ""))) for row in rows))
    
    # 构建表格
    lines = []
    
    # 表头
    header = " | ".join(str(col).ljust(col_widths[col]) for col in columns)
    lines.append(header)
    lines.append("-" * len(header))
    
    # 数据行
    for row in rows:
        line = " | ".join(str(row.get(col, "")).ljust(col_widths[col]) for col in columns)
        lines.append(line)
    
    return "\n".join(lines)


def format_error_response(error: str) -> ResponseType:
    """格式化错误响应"""
    return format_response({"error": error, "success": False})


# ====== MCP 工具函数 ======

@mcp.tool(description="查询固定的alarm.alm_alarm表，根据日期或日期范围查询报警记录")
async def query_table(
    date: str = Field(
        description="查询日期或日期范围。支持格式：1) 单个日期 'YYYY-MM-DD' 如'2025-07-21' 2) 日期范围 'YYYY-MM-DD to YYYY-MM-DD' 如'2025-07-21 to 2025-07-23'。工具会自动查询指定日期或日期范围内的所有报警记录",
        default=""
    ),
    limit: int = Field(description="返回记录数限制，默认100行，设为0表示无限制", default=100)
) -> ResponseType:
    """
    查询固定的alarm.alm_alarm表中指定日期或日期范围的报警数据

    功能说明:
    - 查询固定的当前报警表（alarm.alm_alarm）
    - 支持单个日期或日期范围查询
    - 自动构建查询条件
    - 固定返回列：alarm_first_time, address, alarm_area_name, alarm_type_name

    参数说明:
    - date: 查询日期或日期范围
      * 单个日期: "2025-07-21" (查询该日期当天的所有记录)
      * 日期范围: "2025-07-21 to 2025-07-23" (查询从7月21日到7月23日的所有记录)
      * 留空: "" (查询所有记录)

    - limit: 行数限制
      * 默认100行，防止返回过多数据
      * 设为0表示不限制（谨慎使用）

    返回数据包括:
    - rows: 查询结果数组（包含alarm_first_time, address, alarm_area_name, alarm_type_name）
    - returned_rows: 实际返回的行数
    - total_matching_rows: 符合条件的总行数
    - date_range: 查询的日期或日期范围
    - execution_time: 查询执行时间

    典型使用场景:
    1. 查询特定日期: date="2025-07-21"
    2. 查询日期范围: date="2025-07-21 to 2025-07-23"
    3. 查询所有记录: date=""（留空）
    """
    try:
        driver = await get_sql_driver()

        # 写死的模式名和表名
        schema_name = "alarm"
        table_name = "alm_alarm"

        # 固定查询的列
        columns = "alarm_first_time, address, alarm_area_name, alarm_type_name"

        # 构建查询条件
        conditions = ""
        date_range_desc = ""

        if date.strip():
            from datetime import datetime, timedelta
            try:
                # 检查是否是日期范围格式 (YYYY-MM-DD to YYYY-MM-DD)
                if " to " in date:
                    # 日期范围查询
                    start_date_str, end_date_str = date.split(" to ")
                    start_date_str = start_date_str.strip()
                    end_date_str = end_date_str.strip()

                    # 解析开始和结束日期
                    start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
                    end_date = datetime.strptime(end_date_str, "%Y-%m-%d")

                    # 结束日期需要加1天，以包含结束日期当天的所有记录
                    next_end_date = end_date + timedelta(days=1)

                    conditions = f"alarm_first_time >= '{start_date_str} 00:00:00' AND alarm_first_time < '{next_end_date.strftime('%Y-%m-%d')} 00:00:00'"
                    date_range_desc = f"{start_date_str} 到 {end_date_str}"
                else:
                    # 单个日期查询
                    date_obj = datetime.strptime(date, "%Y-%m-%d")
                    next_date_obj = date_obj + timedelta(days=1)
                    next_date = next_date_obj.strftime("%Y-%m-%d")
                    conditions = f"alarm_first_time >= '{date} 00:00:00' AND alarm_first_time < '{next_date} 00:00:00'"
                    date_range_desc = date

            except ValueError:
                # 如果日期格式错误，返回错误
                return format_error_response(f"日期格式错误，请使用 'YYYY-MM-DD' 或 'YYYY-MM-DD to YYYY-MM-DD' 格式，如: '2025-07-21' 或 '2025-07-21 to 2025-07-23'")
        else:
            date_range_desc = "所有日期"

        # 构建查询语句
        base_query = f"SELECT {columns} FROM {schema_name}.{table_name}"

        params = []
        if conditions:
            base_query += f" WHERE {conditions}"

        # 添加排序（最新的在前）
        base_query += " ORDER BY alarm_first_time DESC"

        # 添加LIMIT（智能处理限制）
        if limit == 0:
            # limit=0 表示用户明确要求不限制记录数
            logger.info("用户要求查询全部记录，不使用LIMIT限制")
        elif limit > 0:
            base_query += f" LIMIT {limit}"
        else:
            # 负数或其他异常值，使用默认值
            base_query += f" LIMIT 100"

        logger.info(f"执行查询: {base_query}")

        result = await driver.execute_query(base_query, params)

        # 同时获取总记录数（如果有条件的话）
        count_query = f"SELECT COUNT(*) as total_count FROM {schema_name}.{table_name}"
        if conditions:
            count_query += f" WHERE {conditions}"

        count_result = await driver.execute_query(count_query, params)
        total_count = count_result.rows[0]['total_count'] if count_result.rows else 0

        response_data = {
            "success": True,
            "schema": schema_name,
            "table": table_name,
            "full_table_name": f"{schema_name}.{table_name}",
            "date_range": date_range_desc,
            "original_date_input": date,
            "conditions": conditions if conditions else "无条件",
            "columns": columns,
            "rows": result.rows,
            "returned_rows": result.row_count,
            "total_matching_rows": total_count,
            "execution_time": f"{result.execution_time:.3f}s",
            "limit": limit
        }

        # 保存结果到本地
        # filename = save_query_result("query_table", {
        #     "date": date,
        #     "limit": limit
        # }, response_data)
        # response_data["saved_file"] = filename

        return format_response(response_data)

    except Exception as e:
        logger.error(f"查询表数据时出错: {e}")
        return format_error_response(str(e))


@mcp.tool(description="查询成都二办区域的报警记录，根据日期或日期范围查询")
async def query_chengdu_area(
    date: str = Field(
        description="查询日期或日期范围。支持格式：1) 单个日期 'YYYY-MM-DD' 如'2025-07-21' 2) 日期范围 'YYYY-MM-DD to YYYY-MM-DD' 如'2025-07-21 to 2025-07-23'。工具会自动查询指定日期或日期范围内成都二办区域的所有报警记录",
        default=""
    ),
    limit: int = Field(description="返回记录数限制，默认100行，设为0表示无限制", default=100)
) -> ResponseType:
    """
    查询成都二办区域的报警数据

    功能说明:
    - 查询固定的当前报警表（alarm.alm_alarm）中成都二办区域的数据
    - 支持单个日期或日期范围查询
    - 自动构建查询条件，固定筛选alarm_area_name = '成都二办'
    - 固定返回列：alarm_first_time, address, alarm_area_name, alarm_type_name

    参数说明:
    - date: 查询日期或日期范围
      * 单个日期: "2025-07-21" (查询该日期当天成都二办的所有记录)
      * 日期范围: "2025-07-21 to 2025-07-23" (查询从7月21日到7月23日成都二办的所有记录)
      * 留空: "" (查询成都二办的所有记录)

    - limit: 行数限制
      * 默认100行，防止返回过多数据
      * 设为0表示不限制（谨慎使用）

    返回数据包括:
    - rows: 查询结果数组（包含alarm_first_time, address, alarm_area_name, alarm_type_name）
    - returned_rows: 实际返回的行数
    - total_matching_rows: 符合条件的总行数
    - area_name: 固定为"成都二办"
    - date_range: 查询的日期或日期范围
    - execution_time: 查询执行时间

    典型使用场景:
    1. 查询成都二办特定日期: date="2025-07-21"
    2. 查询成都二办日期范围: date="2025-07-21 to 2025-07-23"
    3. 查询成都二办所有记录: date=""（留空）
    """
    try:
        driver = await get_sql_driver()

        # 写死的模式名和表名
        schema_name = "alarm"
        table_name = "alm_alarm"

        # 固定查询的列
        columns = "alarm_first_time, address, alarm_area_name, alarm_type_name"

        # 固定的区域条件
        area_condition = "alarm_area_name = '成都二办'"

        # 构建查询条件
        conditions = area_condition
        date_range_desc = ""

        if date.strip():
            from datetime import datetime, timedelta
            try:
                # 检查是否是日期范围格式 (YYYY-MM-DD to YYYY-MM-DD)
                if " to " in date:
                    # 日期范围查询
                    start_date_str, end_date_str = date.split(" to ")
                    start_date_str = start_date_str.strip()
                    end_date_str = end_date_str.strip()

                    # 解析开始和结束日期
                    start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
                    end_date = datetime.strptime(end_date_str, "%Y-%m-%d")

                    # 结束日期需要加1天，以包含结束日期当天的所有记录
                    next_end_date = end_date + timedelta(days=1)

                    date_condition = f"alarm_first_time >= '{start_date_str} 00:00:00' AND alarm_first_time < '{next_end_date.strftime('%Y-%m-%d')} 00:00:00'"
                    conditions = f"{area_condition} AND {date_condition}"
                    date_range_desc = f"{start_date_str} 到 {end_date_str}"
                else:
                    # 单个日期查询
                    date_obj = datetime.strptime(date, "%Y-%m-%d")
                    next_date_obj = date_obj + timedelta(days=1)
                    next_date = next_date_obj.strftime("%Y-%m-%d")
                    date_condition = f"alarm_first_time >= '{date} 00:00:00' AND alarm_first_time < '{next_date} 00:00:00'"
                    conditions = f"{area_condition} AND {date_condition}"
                    date_range_desc = date

            except ValueError:
                # 如果日期格式错误，返回错误
                return format_error_response(f"日期格式错误，请使用 'YYYY-MM-DD' 或 'YYYY-MM-DD to YYYY-MM-DD' 格式，如: '2025-07-21' 或 '2025-07-21 to 2025-07-23'")
        else:
            date_range_desc = "所有日期"

        # 构建查询语句
        base_query = f"SELECT {columns} FROM {schema_name}.{table_name}"

        params = []
        if conditions:
            base_query += f" WHERE {conditions}"

        # 添加排序（最新的在前）
        base_query += " ORDER BY alarm_first_time DESC"

        # 添加LIMIT（智能处理限制）
        if limit == 0:
            # limit=0 表示用户明确要求不限制记录数
            logger.info("用户要求查询全部记录，不使用LIMIT限制")
        elif limit > 0:
            base_query += f" LIMIT {limit}"
        else:
            # 负数或其他异常值，使用默认值
            base_query += f" LIMIT 100"

        logger.info(f"执行查询: {base_query}")

        result = await driver.execute_query(base_query, params)

        # 同时获取总记录数（如果有条件的话）
        count_query = f"SELECT COUNT(*) as total_count FROM {schema_name}.{table_name}"
        if conditions:
            count_query += f" WHERE {conditions}"

        count_result = await driver.execute_query(count_query, params)
        total_count = count_result.rows[0]['total_count'] if count_result.rows else 0

        response_data = {
            "success": True,
            "schema": schema_name,
            "table": table_name,
            "full_table_name": f"{schema_name}.{table_name}",
            "area_name": "成都二办",
            "date_range": date_range_desc,
            "original_date_input": date,
            "conditions": conditions if conditions else "无条件",
            "columns": columns,
            "rows": result.rows,
            "returned_rows": result.row_count,
            "total_matching_rows": total_count,
            "execution_time": f"{result.execution_time:.3f}s",
            "limit": limit
        }

        # 保存结果到本地
        # filename = save_query_result("query_chengdu_area", {
        #     "date": date,
        #     "limit": limit
        # }, response_data)
        # response_data["saved_file"] = filename

        return format_response(response_data)

    except Exception as e:
        logger.error(f"查询成都二办数据时出错: {e}")
        return format_error_response(str(e))


@mcp.tool(description="查询格理特公司区域的报警记录，根据日期或日期范围查询")
async def query_greate_company(
    date: str = Field(
        description="查询日期或日期范围。支持格式：1) 单个日期 'YYYY-MM-DD' 如'2025-07-21' 2) 日期范围 'YYYY-MM-DD to YYYY-MM-DD' 如'2025-07-21 to 2025-07-23'。工具会自动查询指定日期或日期范围内格理特公司区域的所有报警记录",
        default=""
    ),
    limit: int = Field(description="返回记录数限制，默认100行，设为0表示无限制", default=100)
) -> ResponseType:
    """
    查询格理特公司区域的报警数据

    功能说明:
    - 查询固定的当前报警表（alarm.alm_alarm）中格理特公司区域的数据
    - 支持单个日期或日期范围查询
    - 自动构建查询条件，固定筛选alarm_area_name = '格理特公司'
    - 固定返回列：alarm_first_time, address, alarm_area_name, alarm_type_name

    参数说明:
    - date: 查询日期或日期范围
      * 单个日期: "2025-07-21" (查询该日期当天格理特公司的所有记录)
      * 日期范围: "2025-07-21 to 2025-07-23" (查询从7月21日到7月23日格理特公司的所有记录)
      * 留空: "" (查询格理特公司的所有记录)

    - limit: 行数限制
      * 默认100行，防止返回过多数据
      * 设为0表示不限制（谨慎使用）

    返回数据包括:
    - rows: 查询结果数组（包含alarm_first_time, address, alarm_area_name, alarm_type_name）
    - returned_rows: 实际返回的行数
    - total_matching_rows: 符合条件的总行数
    - area_name: 固定为"格理特公司"
    - date_range: 查询的日期或日期范围
    - execution_time: 查询执行时间

    典型使用场景:
    1. 查询格理特公司特定日期: date="2025-07-21"
    2. 查询格理特公司日期范围: date="2025-07-21 to 2025-07-23"
    3. 查询格理特公司所有记录: date=""（留空）
    """
    try:
        driver = await get_sql_driver()

        # 写死的模式名和表名
        schema_name = "alarm"
        table_name = "alm_alarm"

        # 固定查询的列
        columns = "alarm_first_time, address, alarm_area_name, alarm_type_name"

        # 固定的区域条件
        area_condition = "alarm_area_name = '格理特公司'"

        # 构建查询条件
        conditions = area_condition
        date_range_desc = ""

        if date.strip():
            from datetime import datetime, timedelta
            try:
                # 检查是否是日期范围格式 (YYYY-MM-DD to YYYY-MM-DD)
                if " to " in date:
                    # 日期范围查询
                    start_date_str, end_date_str = date.split(" to ")
                    start_date_str = start_date_str.strip()
                    end_date_str = end_date_str.strip()

                    # 解析开始和结束日期
                    start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
                    end_date = datetime.strptime(end_date_str, "%Y-%m-%d")

                    # 结束日期需要加1天，以包含结束日期当天的所有记录
                    next_end_date = end_date + timedelta(days=1)

                    date_condition = f"alarm_first_time >= '{start_date_str} 00:00:00' AND alarm_first_time < '{next_end_date.strftime('%Y-%m-%d')} 00:00:00'"
                    conditions = f"{area_condition} AND {date_condition}"
                    date_range_desc = f"{start_date_str} 到 {end_date_str}"
                else:
                    # 单个日期查询
                    date_obj = datetime.strptime(date, "%Y-%m-%d")
                    next_date_obj = date_obj + timedelta(days=1)
                    next_date = next_date_obj.strftime("%Y-%m-%d")
                    date_condition = f"alarm_first_time >= '{date} 00:00:00' AND alarm_first_time < '{next_date} 00:00:00'"
                    conditions = f"{area_condition} AND {date_condition}"
                    date_range_desc = date

            except ValueError:
                # 如果日期格式错误，返回错误
                return format_error_response(f"日期格式错误，请使用 'YYYY-MM-DD' 或 'YYYY-MM-DD to YYYY-MM-DD' 格式，如: '2025-07-21' 或 '2025-07-21 to 2025-07-23'")
        else:
            date_range_desc = "所有日期"

        # 构建查询语句
        base_query = f"SELECT {columns} FROM {schema_name}.{table_name}"

        params = []
        if conditions:
            base_query += f" WHERE {conditions}"

        # 添加排序（最新的在前）
        base_query += " ORDER BY alarm_first_time DESC"

        # 添加LIMIT（智能处理限制）
        if limit == 0:
            # limit=0 表示用户明确要求不限制记录数
            logger.info("用户要求查询全部记录，不使用LIMIT限制")
        elif limit > 0:
            base_query += f" LIMIT {limit}"
        else:
            # 负数或其他异常值，使用默认值
            base_query += f" LIMIT 100"

        logger.info(f"执行查询: {base_query}")

        result = await driver.execute_query(base_query, params)

        # 同时获取总记录数（如果有条件的话）
        count_query = f"SELECT COUNT(*) as total_count FROM {schema_name}.{table_name}"
        if conditions:
            count_query += f" WHERE {conditions}"

        count_result = await driver.execute_query(count_query, params)
        total_count = count_result.rows[0]['total_count'] if count_result.rows else 0

        response_data = {
            "success": True,
            "schema": schema_name,
            "table": table_name,
            "full_table_name": f"{schema_name}.{table_name}",
            "area_name": "格理特公司",
            "date_range": date_range_desc,
            "original_date_input": date,
            "conditions": conditions if conditions else "无条件",
            "columns": columns,
            "rows": result.rows,
            "returned_rows": result.row_count,
            "total_matching_rows": total_count,
            "execution_time": f"{result.execution_time:.3f}s",
            "limit": limit
        }

        # 保存结果到本地
        # filename = save_query_result("query_greate_company", {
        #     "date": date,
        #     "limit": limit
        # }, response_data)
        # response_data["saved_file"] = filename

        return format_response(response_data)

    except Exception as e:
        logger.error(f"查询格理特公司数据时出错: {e}")
        return format_error_response(str(e))


@mcp.tool(description="查询安全帽监测类型的报警记录，根据日期或日期范围查询")
async def query_helmet_alarms(
    date: str = Field(
        description="查询日期或日期范围。支持格式：1) 单个日期 'YYYY-MM-DD' 如'2025-07-21' 2) 日期范围 'YYYY-MM-DD to YYYY-MM-DD' 如'2025-07-21 to 2025-07-23'。工具会自动查询指定日期或日期范围内安全帽监测类型的所有报警记录",
        default=""
    ),
    limit: int = Field(description="返回记录数限制，默认100行，设为0表示无限制", default=100)
) -> ResponseType:
    """
    查询安全帽监测类型的报警数据

    功能说明:
    - 查询固定的当前报警表（alarm.alm_alarm）中安全帽监测类型的数据
    - 支持单个日期或日期范围查询
    - 自动构建查询条件，固定筛选alarm_type_name = '安全帽监测'
    - 固定返回列：alarm_first_time, address, alarm_area_name, alarm_type_name

    参数说明:
    - date: 查询日期或日期范围
      * 单个日期: "2025-07-21" (查询该日期当天安全帽监测的所有记录)
      * 日期范围: "2025-07-21 to 2025-07-23" (查询从7月21日到7月23日安全帽监测的所有记录)
      * 留空: "" (查询安全帽监测的所有记录)

    - limit: 行数限制
      * 默认100行，防止返回过多数据
      * 设为0表示不限制（谨慎使用）

    返回数据包括:
    - rows: 查询结果数组（包含alarm_first_time, address, alarm_area_name, alarm_type_name）
    - returned_rows: 实际返回的行数
    - total_matching_rows: 符合条件的总行数
    - alarm_type: 固定为"安全帽监测"
    - date_range: 查询的日期或日期范围
    - execution_time: 查询执行时间

    典型使用场景:
    1. 查询安全帽监测特定日期: date="2025-07-21"
    2. 查询安全帽监测日期范围: date="2025-07-21 to 2025-07-23"
    3. 查询安全帽监测所有记录: date=""（留空）
    """
    try:
        driver = await get_sql_driver()

        # 写死的模式名和表名
        schema_name = "alarm"
        table_name = "alm_alarm"

        # 固定查询的列
        columns = "alarm_first_time, address, alarm_area_name, alarm_type_name"

        # 固定的报警类型条件
        type_condition = "alarm_type_name = '安全帽监测'"

        # 构建查询条件
        conditions = type_condition
        date_range_desc = ""

        if date.strip():
            from datetime import datetime, timedelta
            try:
                # 检查是否是日期范围格式 (YYYY-MM-DD to YYYY-MM-DD)
                if " to " in date:
                    # 日期范围查询
                    start_date_str, end_date_str = date.split(" to ")
                    start_date_str = start_date_str.strip()
                    end_date_str = end_date_str.strip()

                    # 解析开始和结束日期
                    start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
                    end_date = datetime.strptime(end_date_str, "%Y-%m-%d")

                    # 结束日期需要加1天，以包含结束日期当天的所有记录
                    next_end_date = end_date + timedelta(days=1)

                    date_condition = f"alarm_first_time >= '{start_date_str} 00:00:00' AND alarm_first_time < '{next_end_date.strftime('%Y-%m-%d')} 00:00:00'"
                    conditions = f"{type_condition} AND {date_condition}"
                    date_range_desc = f"{start_date_str} 到 {end_date_str}"
                else:
                    # 单个日期查询
                    date_obj = datetime.strptime(date, "%Y-%m-%d")
                    next_date_obj = date_obj + timedelta(days=1)
                    next_date = next_date_obj.strftime("%Y-%m-%d")
                    date_condition = f"alarm_first_time >= '{date} 00:00:00' AND alarm_first_time < '{next_date} 00:00:00'"
                    conditions = f"{type_condition} AND {date_condition}"
                    date_range_desc = date

            except ValueError:
                # 如果日期格式错误，返回错误
                return format_error_response(f"日期格式错误，请使用 'YYYY-MM-DD' 或 'YYYY-MM-DD to YYYY-MM-DD' 格式，如: '2025-07-21' 或 '2025-07-21 to 2025-07-23'")
        else:
            date_range_desc = "所有日期"

        # 构建查询语句
        base_query = f"SELECT {columns} FROM {schema_name}.{table_name}"

        params = []
        if conditions:
            base_query += f" WHERE {conditions}"

        # 添加排序（最新的在前）
        base_query += " ORDER BY alarm_first_time DESC"

        # 添加LIMIT（智能处理限制）
        if limit == 0:
            # limit=0 表示用户明确要求不限制记录数
            logger.info("用户要求查询全部记录，不使用LIMIT限制")
        elif limit > 0:
            base_query += f" LIMIT {limit}"
        else:
            # 负数或其他异常值，使用默认值
            base_query += f" LIMIT 100"

        logger.info(f"执行查询: {base_query}")

        result = await driver.execute_query(base_query, params)

        # 同时获取总记录数（如果有条件的话）
        count_query = f"SELECT COUNT(*) as total_count FROM {schema_name}.{table_name}"
        if conditions:
            count_query += f" WHERE {conditions}"

        count_result = await driver.execute_query(count_query, params)
        total_count = count_result.rows[0]['total_count'] if count_result.rows else 0

        response_data = {
            "success": True,
            "schema": schema_name,
            "table": table_name,
            "full_table_name": f"{schema_name}.{table_name}",
            "alarm_type": "安全帽监测",
            "date_range": date_range_desc,
            "original_date_input": date,
            "conditions": conditions if conditions else "无条件",
            "columns": columns,
            "rows": result.rows,
            "returned_rows": result.row_count,
            "total_matching_rows": total_count,
            "execution_time": f"{result.execution_time:.3f}s",
            "limit": limit
        }

        # 保存结果到本地
        # filename = save_query_result("query_helmet_alarms", {
        #     "date": date,
        #     "limit": limit
        # }, response_data)
        # response_data["saved_file"] = filename

        return format_response(response_data)

    except Exception as e:
        logger.error(f"查询安全帽监测数据时出错: {e}")
        return format_error_response(str(e))


@mcp.tool(description="查询红区闯入监测类型的报警记录，根据日期或日期范围查询")
async def query_intrusion_alarms(
    date: str = Field(
        description="查询日期或日期范围。支持格式：1) 单个日期 'YYYY-MM-DD' 如'2025-07-21' 2) 日期范围 'YYYY-MM-DD to YYYY-MM-DD' 如'2025-07-21 to 2025-07-23'。工具会自动查询指定日期或日期范围内红区闯入监测类型的所有报警记录",
        default=""
    ),
    limit: int = Field(description="返回记录数限制，默认100行，设为0表示无限制", default=100)
) -> ResponseType:
    """
    查询红区闯入监测类型的报警数据

    功能说明:
    - 查询固定的当前报警表（alarm.alm_alarm）中红区闯入监测类型的数据
    - 支持单个日期或日期范围查询
    - 自动构建查询条件，固定筛选alarm_type_name = '红区闯入监测'
    - 固定返回列：alarm_first_time, address, alarm_area_name, alarm_type_name

    参数说明:
    - date: 查询日期或日期范围
      * 单个日期: "2025-07-21" (查询该日期当天红区闯入监测的所有记录)
      * 日期范围: "2025-07-21 to 2025-07-23" (查询从7月21日到7月23日红区闯入监测的所有记录)
      * 留空: "" (查询红区闯入监测的所有记录)

    - limit: 行数限制
      * 默认100行，防止返回过多数据
      * 设为0表示不限制（谨慎使用）

    返回数据包括:
    - rows: 查询结果数组（包含alarm_first_time, address, alarm_area_name, alarm_type_name）
    - returned_rows: 实际返回的行数
    - total_matching_rows: 符合条件的总行数
    - alarm_type: 固定为"红区闯入监测"
    - date_range: 查询的日期或日期范围
    - execution_time: 查询执行时间

    典型使用场景:
    1. 查询红区闯入监测特定日期: date="2025-07-21"
    2. 查询红区闯入监测日期范围: date="2025-07-21 to 2025-07-23"
    3. 查询红区闯入监测所有记录: date=""（留空）
    """
    try:
        driver = await get_sql_driver()

        # 写死的模式名和表名
        schema_name = "alarm"
        table_name = "alm_alarm"

        # 固定查询的列
        columns = "alarm_first_time, address, alarm_area_name, alarm_type_name"

        # 固定的报警类型条件
        type_condition = "alarm_type_name = '红区闯入监测'"

        # 构建查询条件
        conditions = type_condition
        date_range_desc = ""

        if date.strip():
            from datetime import datetime, timedelta
            try:
                # 检查是否是日期范围格式 (YYYY-MM-DD to YYYY-MM-DD)
                if " to " in date:
                    # 日期范围查询
                    start_date_str, end_date_str = date.split(" to ")
                    start_date_str = start_date_str.strip()
                    end_date_str = end_date_str.strip()

                    # 解析开始和结束日期
                    start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
                    end_date = datetime.strptime(end_date_str, "%Y-%m-%d")

                    # 结束日期需要加1天，以包含结束日期当天的所有记录
                    next_end_date = end_date + timedelta(days=1)

                    date_condition = f"alarm_first_time >= '{start_date_str} 00:00:00' AND alarm_first_time < '{next_end_date.strftime('%Y-%m-%d')} 00:00:00'"
                    conditions = f"{type_condition} AND {date_condition}"
                    date_range_desc = f"{start_date_str} 到 {end_date_str}"
                else:
                    # 单个日期查询
                    date_obj = datetime.strptime(date, "%Y-%m-%d")
                    next_date_obj = date_obj + timedelta(days=1)
                    next_date = next_date_obj.strftime("%Y-%m-%d")
                    date_condition = f"alarm_first_time >= '{date} 00:00:00' AND alarm_first_time < '{next_date} 00:00:00'"
                    conditions = f"{type_condition} AND {date_condition}"
                    date_range_desc = date

            except ValueError:
                # 如果日期格式错误，返回错误
                return format_error_response(f"日期格式错误，请使用 'YYYY-MM-DD' 或 'YYYY-MM-DD to YYYY-MM-DD' 格式，如: '2025-07-21' 或 '2025-07-21 to 2025-07-23'")
        else:
            date_range_desc = "所有日期"

        # 构建查询语句
        base_query = f"SELECT {columns} FROM {schema_name}.{table_name}"

        params = []
        if conditions:
            base_query += f" WHERE {conditions}"

        # 添加排序（最新的在前）
        base_query += " ORDER BY alarm_first_time DESC"

        # 添加LIMIT（智能处理限制）
        if limit == 0:
            # limit=0 表示用户明确要求不限制记录数
            logger.info("用户要求查询全部记录，不使用LIMIT限制")
        elif limit > 0:
            base_query += f" LIMIT {limit}"
        else:
            # 负数或其他异常值，使用默认值
            base_query += f" LIMIT 100"

        logger.info(f"执行查询: {base_query}")

        result = await driver.execute_query(base_query, params)

        # 同时获取总记录数（如果有条件的话）
        count_query = f"SELECT COUNT(*) as total_count FROM {schema_name}.{table_name}"
        if conditions:
            count_query += f" WHERE {conditions}"

        count_result = await driver.execute_query(count_query, params)
        total_count = count_result.rows[0]['total_count'] if count_result.rows else 0

        response_data = {
            "success": True,
            "schema": schema_name,
            "table": table_name,
            "full_table_name": f"{schema_name}.{table_name}",
            "alarm_type": "红区闯入监测",
            "date_range": date_range_desc,
            "original_date_input": date,
            "conditions": conditions if conditions else "无条件",
            "columns": columns,
            "rows": result.rows,
            "returned_rows": result.row_count,
            "total_matching_rows": total_count,
            "execution_time": f"{result.execution_time:.3f}s",
            "limit": limit
        }

        # 保存结果到本地
        # filename = save_query_result("query_intrusion_alarms", {
        #     "date": date,
        #     "limit": limit
        # }, response_data)
        # response_data["saved_file"] = filename

        return format_response(response_data)

    except Exception as e:
        logger.error(f"查询红区闯入监测数据时出错: {e}")
        return format_error_response(str(e))


@mcp.tool(description="查询红区进入预警类型的报警记录，根据日期或日期范围查询")
async def query_invasion_alarms(
    date: str = Field(
        description="查询日期或日期范围。支持格式：1) 单个日期 'YYYY-MM-DD' 如'2025-07-21' 2) 日期范围 'YYYY-MM-DD to YYYY-MM-DD' 如'2025-07-21 to 2025-07-23'。工具会自动查询指定日期或日期范围内红区进入预警类型的所有报警记录",
        default=""
    ),
    limit: int = Field(description="返回记录数限制，默认100行，设为0表示无限制", default=100)
) -> ResponseType:
    """
    查询红区进入预警类型的报警数据

    功能说明:
    - 查询固定的当前报警表（alarm.alm_alarm）中红区进入预警类型的数据
    - 支持单个日期或日期范围查询
    - 自动构建查询条件，固定筛选alarm_type_name = '红区进入预警'
    - 固定返回列：alarm_first_time, address, alarm_area_name, alarm_type_name

    参数说明:
    - date: 查询日期或日期范围
      * 单个日期: "2025-07-21" (查询该日期当天红区进入预警的所有记录)
      * 日期范围: "2025-07-21 to 2025-07-23" (查询从7月21日到7月23日红区进入预警的所有记录)
      * 留空: "" (查询红区进入预警的所有记录)

    - limit: 行数限制
      * 默认100行，防止返回过多数据
      * 设为0表示不限制（谨慎使用）

    返回数据包括:
    - rows: 查询结果数组（包含alarm_first_time, address, alarm_area_name, alarm_type_name）
    - returned_rows: 实际返回的行数
    - total_matching_rows: 符合条件的总行数
    - alarm_type: 固定为"红区进入预警"
    - date_range: 查询的日期或日期范围
    - execution_time: 查询执行时间

    典型使用场景:
    1. 查询红区进入预警特定日期: date="2025-07-21"
    2. 查询红区进入预警日期范围: date="2025-07-21 to 2025-07-23"
    3. 查询红区进入预警所有记录: date=""（留空）
    """
    try:
        driver = await get_sql_driver()

        # 写死的模式名和表名
        schema_name = "alarm"
        table_name = "alm_alarm"

        # 固定查询的列
        columns = "alarm_first_time, address, alarm_area_name, alarm_type_name"

        # 固定的报警类型条件
        type_condition = "alarm_type_name = '红区进入预警'"

        # 构建查询条件
        conditions = type_condition
        date_range_desc = ""

        if date.strip():
            from datetime import datetime, timedelta
            try:
                # 检查是否是日期范围格式 (YYYY-MM-DD to YYYY-MM-DD)
                if " to " in date:
                    # 日期范围查询
                    start_date_str, end_date_str = date.split(" to ")
                    start_date_str = start_date_str.strip()
                    end_date_str = end_date_str.strip()

                    # 解析开始和结束日期
                    start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
                    end_date = datetime.strptime(end_date_str, "%Y-%m-%d")

                    # 结束日期需要加1天，以包含结束日期当天的所有记录
                    next_end_date = end_date + timedelta(days=1)

                    date_condition = f"alarm_first_time >= '{start_date_str} 00:00:00' AND alarm_first_time < '{next_end_date.strftime('%Y-%m-%d')} 00:00:00'"
                    conditions = f"{type_condition} AND {date_condition}"
                    date_range_desc = f"{start_date_str} 到 {end_date_str}"
                else:
                    # 单个日期查询
                    date_obj = datetime.strptime(date, "%Y-%m-%d")
                    next_date_obj = date_obj + timedelta(days=1)
                    next_date = next_date_obj.strftime("%Y-%m-%d")
                    date_condition = f"alarm_first_time >= '{date} 00:00:00' AND alarm_first_time < '{next_date} 00:00:00'"
                    conditions = f"{type_condition} AND {date_condition}"
                    date_range_desc = date

            except ValueError:
                # 如果日期格式错误，返回错误
                return format_error_response(f"日期格式错误，请使用 'YYYY-MM-DD' 或 'YYYY-MM-DD to YYYY-MM-DD' 格式，如: '2025-07-21' 或 '2025-07-21 to 2025-07-23'")
        else:
            date_range_desc = "所有日期"

        # 构建查询语句
        base_query = f"SELECT {columns} FROM {schema_name}.{table_name}"

        params = []
        if conditions:
            base_query += f" WHERE {conditions}"

        # 添加排序（最新的在前）
        base_query += " ORDER BY alarm_first_time DESC"

        # 添加LIMIT（智能处理限制）
        if limit == 0:
            # limit=0 表示用户明确要求不限制记录数
            logger.info("用户要求查询全部记录，不使用LIMIT限制")
        elif limit > 0:
            base_query += f" LIMIT {limit}"
        else:
            # 负数或其他异常值，使用默认值
            base_query += f" LIMIT 100"

        logger.info(f"执行查询: {base_query}")

        result = await driver.execute_query(base_query, params)

        # 同时获取总记录数（如果有条件的话）
        count_query = f"SELECT COUNT(*) as total_count FROM {schema_name}.{table_name}"
        if conditions:
            count_query += f" WHERE {conditions}"

        count_result = await driver.execute_query(count_query, params)
        total_count = count_result.rows[0]['total_count'] if count_result.rows else 0

        response_data = {
            "success": True,
            "schema": schema_name,
            "table": table_name,
            "full_table_name": f"{schema_name}.{table_name}",
            "alarm_type": "红区进入预警",
            "date_range": date_range_desc,
            "original_date_input": date,
            "conditions": conditions if conditions else "无条件",
            "columns": columns,
            "rows": result.rows,
            "returned_rows": result.row_count,
            "total_matching_rows": total_count,
            "execution_time": f"{result.execution_time:.3f}s",
            "limit": limit
        }

        # 保存结果到本地
        # filename = save_query_result("query_invasion_alarms", {
        #     "date": date,
        #     "limit": limit
        # }, response_data)
        # response_data["saved_file"] = filename

        return format_response(response_data)

    except Exception as e:
        logger.error(f"查询红区进入预警数据时出错: {e}")
        return format_error_response(str(e))


@mcp.tool(description="查询工作服监测类型的报警记录，根据日期或日期范围查询")
async def query_workclothes_alarms(
    date: str = Field(
        description="查询日期或日期范围。支持格式：1) 单个日期 'YYYY-MM-DD' 如'2025-07-21' 2) 日期范围 'YYYY-MM-DD to YYYY-MM-DD' 如'2025-07-21 to 2025-07-23'。工具会自动查询指定日期或日期范围内工作服监测类型的所有报警记录",
        default=""
    ),
    limit: int = Field(description="返回记录数限制，默认100行，设为0表示无限制", default=100)
) -> ResponseType:
    """
    查询工作服监测类型的报警数据

    功能说明:
    - 查询固定的当前报警表（alarm.alm_alarm）中工作服监测类型的数据
    - 支持单个日期或日期范围查询
    - 自动构建查询条件，固定筛选alarm_type_name = '工作服监测'
    - 固定返回列：alarm_first_time, address, alarm_area_name, alarm_type_name

    参数说明:
    - date: 查询日期或日期范围
      * 单个日期: "2025-07-21" (查询该日期当天工作服监测的所有记录)
      * 日期范围: "2025-07-21 to 2025-07-23" (查询从7月21日到7月23日工作服监测的所有记录)
      * 留空: "" (查询工作服监测的所有记录)

    - limit: 行数限制
      * 默认100行，防止返回过多数据
      * 设为0表示不限制（谨慎使用）

    返回数据包括:
    - rows: 查询结果数组（包含alarm_first_time, address, alarm_area_name, alarm_type_name）
    - returned_rows: 实际返回的行数
    - total_matching_rows: 符合条件的总行数
    - alarm_type: 固定为"工作服监测"
    - date_range: 查询的日期或日期范围
    - execution_time: 查询执行时间

    典型使用场景:
    1. 查询工作服监测特定日期: date="2025-07-21"
    2. 查询工作服监测日期范围: date="2025-07-21 to 2025-07-23"
    3. 查询工作服监测所有记录: date=""（留空）
    """
    try:
        driver = await get_sql_driver()

        # 写死的模式名和表名
        schema_name = "alarm"
        table_name = "alm_alarm"

        # 固定查询的列
        columns = "alarm_first_time, address, alarm_area_name, alarm_type_name"

        # 固定的报警类型条件
        type_condition = "alarm_type_name = '工作服监测'"

        # 构建查询条件
        conditions = type_condition
        date_range_desc = ""

        if date.strip():
            from datetime import datetime, timedelta
            try:
                # 检查是否是日期范围格式 (YYYY-MM-DD to YYYY-MM-DD)
                if " to " in date:
                    # 日期范围查询
                    start_date_str, end_date_str = date.split(" to ")
                    start_date_str = start_date_str.strip()
                    end_date_str = end_date_str.strip()

                    # 解析开始和结束日期
                    start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
                    end_date = datetime.strptime(end_date_str, "%Y-%m-%d")

                    # 结束日期需要加1天，以包含结束日期当天的所有记录
                    next_end_date = end_date + timedelta(days=1)

                    date_condition = f"alarm_first_time >= '{start_date_str} 00:00:00' AND alarm_first_time < '{next_end_date.strftime('%Y-%m-%d')} 00:00:00'"
                    conditions = f"{type_condition} AND {date_condition}"
                    date_range_desc = f"{start_date_str} 到 {end_date_str}"
                else:
                    # 单个日期查询
                    date_obj = datetime.strptime(date, "%Y-%m-%d")
                    next_date_obj = date_obj + timedelta(days=1)
                    next_date = next_date_obj.strftime("%Y-%m-%d")
                    date_condition = f"alarm_first_time >= '{date} 00:00:00' AND alarm_first_time < '{next_date} 00:00:00'"
                    conditions = f"{type_condition} AND {date_condition}"
                    date_range_desc = date

            except ValueError:
                # 如果日期格式错误，返回错误
                return format_error_response(f"日期格式错误，请使用 'YYYY-MM-DD' 或 'YYYY-MM-DD to YYYY-MM-DD' 格式，如: '2025-07-21' 或 '2025-07-21 to 2025-07-23'")
        else:
            date_range_desc = "所有日期"

        # 构建查询语句
        base_query = f"SELECT {columns} FROM {schema_name}.{table_name}"

        params = []
        if conditions:
            base_query += f" WHERE {conditions}"

        # 添加排序（最新的在前）
        base_query += " ORDER BY alarm_first_time DESC"

        # 添加LIMIT（智能处理限制）
        if limit == 0:
            # limit=0 表示用户明确要求不限制记录数
            logger.info("用户要求查询全部记录，不使用LIMIT限制")
        elif limit > 0:
            base_query += f" LIMIT {limit}"
        else:
            # 负数或其他异常值，使用默认值
            base_query += f" LIMIT 100"

        logger.info(f"执行查询: {base_query}")

        result = await driver.execute_query(base_query, params)

        # 同时获取总记录数（如果有条件的话）
        count_query = f"SELECT COUNT(*) as total_count FROM {schema_name}.{table_name}"
        if conditions:
            count_query += f" WHERE {conditions}"

        count_result = await driver.execute_query(count_query, params)
        total_count = count_result.rows[0]['total_count'] if count_result.rows else 0

        response_data = {
            "success": True,
            "schema": schema_name,
            "table": table_name,
            "full_table_name": f"{schema_name}.{table_name}",
            "alarm_type": "工作服监测",
            "date_range": date_range_desc,
            "original_date_input": date,
            "conditions": conditions if conditions else "无条件",
            "columns": columns,
            "rows": result.rows,
            "returned_rows": result.row_count,
            "total_matching_rows": total_count,
            "execution_time": f"{result.execution_time:.3f}s",
            "limit": limit
        }

        # 保存结果到本地
        # filename = save_query_result("query_workclothes_alarms", {
        #     "date": date,
        #     "limit": limit
        # }, response_data)
        # response_data["saved_file"] = filename

        return format_response(response_data)

    except Exception as e:
        logger.error(f"查询工作服监测数据时出错: {e}")
        return format_error_response(str(e))


# ====== 主程序 ======

async def main():
    """主程序入口"""
    # 初始化日志
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # 记录Windows系统的事件循环策略设置
    if platform.system() == "Windows":
        logger.info("Windows系统: 已设置SelectorEventLoopPolicy以支持psycopg异步操作")
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="简化版PostgreSQL MCP Server")
    parser.add_argument("database_url", help="数据库连接URL", nargs="?")
    parser.add_argument(
        "--transport",
        type=str,
        choices=["stdio", "sse"],
        default="sse",
        help="传输协议: stdio 或 sse (默认: sse)"
    )
    parser.add_argument(
        "--sse-host",
        type=str,
        default="0.0.0.0",
        help="SSE服务器绑定地址 (默认: 0.0.0.0)"
    )
    parser.add_argument(
        "--sse-port",
        type=int,
        default=8080,
        help="SSE服务器端口 (默认: 8080)"
    )
    parser.add_argument(
        "--log-level",
        type=str,
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日志级别 (默认: INFO)"
    )
    
    args = parser.parse_args()
    
    # 设置日志级别
    logger.setLevel(getattr(logging, args.log_level))
    
    # 获取数据库URL
    database_url = os.environ.get("DATABASE_URL", args.database_url)
    
    if not database_url:
        logger.error("错误: 未提供数据库URL。请通过 'DATABASE_URL' 环境变量或命令行参数提供。")
        sys.exit(1)
    
    # 初始化数据库连接池
    try:
        await db_pool.connect(database_url)
        logger.info("成功连接到数据库并初始化连接池")
    except Exception as e:
        logger.error(f"无法连接到数据库: {obfuscate_password(str(e))}")
        logger.error("MCP服务器将启动，但数据库操作将失败，直到建立有效连接。")
        sys.exit(1)
    
    # 设置优雅关闭
    shutdown_event = asyncio.Event()
    
    def signal_handler(signum, _):
        logger.info(f"接收到信号 {signum}，准备关闭服务器...")
        shutdown_event.set()
    
    # 注册信号处理器
    try:
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    except ValueError:
        # Windows上可能不支持某些信号
        logger.warning("信号处理在此平台上不受支持")
    
    # 启动服务器
    try:
        if args.transport == "stdio":
            logger.info("使用 stdio 传输协议启动MCP服务器")
            await mcp.run_stdio_async()
        else:
            logger.info(f"使用 SSE 传输协议启动MCP服务器，地址: {args.sse_host}:{args.sse_port}")
            mcp.settings.host = args.sse_host
            mcp.settings.port = args.sse_port
            await mcp.run_sse_async()
    except KeyboardInterrupt:
        logger.info("接收到键盘中断信号")
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        sys.exit(1)
    finally:
        # 清理资源
        await cleanup()


async def cleanup():
    """清理资源"""
    try:
        await db_pool.close()
        logger.info("已关闭数据库连接")
    except Exception as e:
        logger.error(f"关闭数据库连接时出错: {e}")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序运行时出错: {e}")
        sys.exit(1)

