#!/usr/bin/env python3
"""
HTTP API 服务器 - 为 MCP 客户端提供 REST API 接口

架构: Postman → HTTP Server (FastAPI) → MCPClient → MCP Server → PostgreSQL

功能:
- 提供 HTTP API 接口供 Postman 等工具调用
- 管理单例 MCPClient 实例
- 处理查询请求并返回结构化响应
- 提供健康检查和工具列表接口
"""

import asyncio
import logging
import os
import sys
from contextlib import asynccontextmanager
from typing import Optional, Dict, Any, List

import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

# 导入重构后的 MCP 客户端
from llm_mcpclient import MCPClient, MCPConfig, QueryResponse

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局 MCP 客户端实例
mcp_client: Optional[MCPClient] = None


class QueryRequest(BaseModel):
    """查询请求模型"""
    query: str = Field(..., description="要执行的查询文本", min_length=1)
    mcp_server_url: Optional[str] = Field(
        default=None, 
        description="MCP服务器URL，如果不提供则使用默认配置"
    )


class QueryResponseModel(BaseModel):
    """查询响应模型"""
    success: bool
    query: str
    response: str
    tool_calls: List[Dict[str, Any]]
    execution_time: float
    error: Optional[str] = None


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str
    mcp_connected: bool
    mcp_server_url: Optional[str]
    message: str


class ToolInfo(BaseModel):
    """工具信息模型"""
    name: str
    description: str
    parameters: Dict[str, Any]


class ToolsResponse(BaseModel):
    """工具列表响应模型"""
    success: bool
    tools: List[ToolInfo]
    count: int
    error: Optional[str] = None


class ModelInfoResponse(BaseModel):
    """模型信息响应模型"""
    current_model: str
    supports_tools: bool
    recommended_models: List[str]
    message: str


# 配置管理
class ServerConfig:
    """服务器配置"""
    def __init__(self):
        # MCP 服务器配置
        self.default_mcp_server_url = os.getenv(
            "MCP_SERVER_URL", 
            "http://localhost:8080/sse"
        )
        
        # Ollama 配置
        self.ollama_api_key = os.getenv("OLLAMA_API_KEY", "ollama")
        self.ollama_base_url = os.getenv(
            "OLLAMA_BASE_URL", 
            "http://*************:11434/v1"
        )
        self.ollama_model = os.getenv("OLLAMA_MODEL", "qwen2.5:32b")

        # 支持工具调用的模型列表
        self.tool_supported_models = [
            "qwen2.5:7b", "qwen2.5:14b", "qwen2.5:32b",
            "llama3.1:8b", "llama3.1:70b",
            "mistral:7b", "mistral-nemo:12b",
            "codellama:7b", "codellama:13b"
        ]
        
        # HTTP 服务器配置
        self.host = os.getenv("HTTP_HOST", "0.0.0.0")
        self.port = int(os.getenv("HTTP_PORT", "8000"))


server_config = ServerConfig()


async def get_mcp_client() -> MCPClient:
    """获取或创建 MCP 客户端实例"""
    global mcp_client
    
    if mcp_client is None:
        # 创建 MCP 配置
        config = MCPConfig(
            openai_api_key=server_config.ollama_api_key,
            base_url=server_config.ollama_base_url,
            model=server_config.ollama_model,
            mcp_server_url=server_config.default_mcp_server_url
        )
        
        # 创建客户端实例
        mcp_client = MCPClient(config)
        
        # 尝试连接到默认 MCP 服务器
        try:
            await mcp_client.connect_to_server(
                server_config.default_mcp_server_url, 
                verbose=False
            )
            logger.info(f"已连接到默认 MCP 服务器: {server_config.default_mcp_server_url}")
        except Exception as e:
            logger.warning(f"无法连接到默认 MCP 服务器: {e}")
    
    return mcp_client


async def ensure_mcp_connection(client: MCPClient, server_url: Optional[str] = None) -> bool:
    """确保 MCP 客户端已连接"""
    target_url = server_url or server_config.default_mcp_server_url
    
    # 如果已连接到目标服务器，直接返回
    if client.is_connected and client.server_url == target_url:
        return True
    
    # 尝试连接到指定服务器
    try:
        await client.connect_to_server(target_url, verbose=False)
        return True
    except Exception as e:
        logger.error(f"连接到 MCP 服务器失败 ({target_url}): {e}")
        return False


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("正在启动 HTTP API 服务器...")
    
    # 预创建 MCP 客户端
    try:
        await get_mcp_client()
        logger.info("MCP 客户端初始化完成")
    except Exception as e:
        logger.warning(f"MCP 客户端初始化失败: {e}")
    
    yield
    
    # 关闭时清理
    logger.info("正在关闭 HTTP API 服务器...")
    global mcp_client
    if mcp_client:
        try:
            await mcp_client.cleanup()
            logger.info("MCP 客户端资源已清理")
        except Exception as e:
            logger.error(f"清理 MCP 客户端时出错: {e}")


# 创建 FastAPI 应用
app = FastAPI(
    title="MCP HTTP API Server",
    description="为 MCP 客户端提供 HTTP API 接口",
    version="1.0.0",
    lifespan=lifespan
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/", summary="根路径", description="API 服务器信息")
async def root():
    """根路径 - 返回 API 信息"""
    return {
        "message": "MCP HTTP API Server",
        "version": "1.0.0",
        "endpoints": {
            "query": "POST /query - 执行查询",
            "health": "GET /health - 健康检查",
            "tools": "GET /tools - 获取可用工具列表",
            "model": "GET /model - 获取模型信息"
        }
    }


@app.get("/health", response_model=HealthResponse, summary="健康检查")
async def health_check():
    """健康检查端点"""
    try:
        client = await get_mcp_client()
        
        return HealthResponse(
            status="healthy",
            mcp_connected=client.is_connected,
            mcp_server_url=client.server_url,
            message="服务运行正常" if client.is_connected else "MCP 服务器未连接"
        )
    except Exception as e:
        return HealthResponse(
            status="unhealthy",
            mcp_connected=False,
            mcp_server_url=None,
            message=f"服务异常: {str(e)}"
        )


@app.get("/model", response_model=ModelInfoResponse, summary="获取模型信息")
async def get_model_info():
    """获取当前模型信息和工具支持状态"""
    current_model = server_config.ollama_model
    supports_tools = current_model in server_config.tool_supported_models

    if supports_tools:
        message = f"当前模型 {current_model} 支持工具调用，可以正常执行数据库查询。"
    else:
        message = f"当前模型 {current_model} 不支持工具调用，将使用提示词模式进行查询。建议使用支持工具调用的模型以获得更好的查询体验。"

    return ModelInfoResponse(
        current_model=current_model,
        supports_tools=supports_tools,
        recommended_models=server_config.tool_supported_models,
        message=message
    )


@app.get("/tools", response_model=ToolsResponse, summary="获取可用工具列表")
async def get_tools():
    """获取 MCP 服务器提供的可用工具列表"""
    try:
        client = await get_mcp_client()
        
        # 确保连接
        if not await ensure_mcp_connection(client):
            return ToolsResponse(
                success=False,
                tools=[],
                count=0,
                error="无法连接到 MCP 服务器"
            )
        
        # 获取工具列表
        tools_data = await client.get_available_tools()
        tools = [
            ToolInfo(
                name=tool["name"],
                description=tool["description"] or "无描述",
                parameters=tool["parameters"] or {}
            )
            for tool in tools_data
        ]
        
        return ToolsResponse(
            success=True,
            tools=tools,
            count=len(tools)
        )
        
    except Exception as e:
        logger.error(f"获取工具列表时出错: {e}")
        return ToolsResponse(
            success=False,
            tools=[],
            count=0,
            error=f"获取工具列表失败: {str(e)}"
        )


def is_database_related_query(query: str) -> bool:
    """判断查询是否与数据库相关"""
    # 数据库相关关键词
    db_keywords = [
        # 中文关键词
        "查询", "统计", "分析", "报警", "数据", "记录", "表", "数据库",
        "安全帽", "工作服", "红区", "闯入", "进入", "预警", "监测",
        "成都二办", "格理特公司", "格理特", "成都",
        "今天", "昨天", "明天", "日期", "时间", "月份", "年份",
        "多少", "几个", "几条", "总数", "数量", "统计","上周","本周"
        "最新", "最近", "历史", "趋势",
        "地址", "区域", "位置", "地点",

        # 英文关键词
        "query", "select", "count", "sum", "avg", "max", "min",
        "alarm", "data", "record", "table", "database",
        "helmet", "workclothes", "intrusion", "invasion",

        # 日期格式
        "2025", "2024", "2023", "-07-", "-08-", "-09-",

        # SQL相关
        "WHERE", "SELECT", "FROM", "ORDER BY", "LIMIT"
    ]

    # 非数据库相关关键词（优先级更高）
    non_db_keywords = [
        "你好", "hello", "hi", "嗨", "您好",
        "天气", "weather", "温度", "气温",
        "你是谁", "who are you", "介绍", "自我介绍",
        "帮助", "help", "使用方法", "怎么用",
        "谢谢", "thank", "感谢",
        "再见", "bye", "goodbye", "拜拜",
        "测试", "test", "sfsf", "asdf", "123",
        "随便", "无聊", "闲聊"
    ]

    query_lower = query.lower()

    # 首先检查是否包含非数据库关键词
    for keyword in non_db_keywords:
        if keyword in query_lower:
            return False

    # 然后检查是否包含数据库关键词
    for keyword in db_keywords:
        if keyword in query_lower:
            return True

    # 如果都没有匹配，默认不调用数据库
    return False


@app.post("/query", response_model=QueryResponseModel, summary="执行查询")
async def execute_query(request: QueryRequest):
    """
    执行查询请求

    接收用户查询，通过 MCP 客户端处理并返回结果
    """
    try:
        logger.info(f"收到查询请求: {request.query}")

        # 判断是否需要调用MCP工具
        if not is_database_related_query(request.query):
            logger.info("查询不涉及数据库，直接调用LLM")
            # 直接调用Ollama，不使用MCP工具
            client = await get_mcp_client()
            result: QueryResponse = await client.call_ollama_directly(request.query)

            # 转换为响应模型
            return QueryResponseModel(
                success=result.success,
                query=result.query,
                response=result.response,
                tool_calls=result.tool_calls,
                execution_time=result.execution_time,
                error=result.error
            )

        logger.info("查询涉及数据库，使用MCP工具")

        client = await get_mcp_client()

        # 确保连接到指定的 MCP 服务器
        target_url = request.mcp_server_url or server_config.default_mcp_server_url
        if not await ensure_mcp_connection(client, target_url):
            raise HTTPException(
                status_code=503,
                detail=f"无法连接到 MCP 服务器: {target_url}"
            )

        # 执行查询
        result: QueryResponse = await client.process_query(request.query)

        # 转换为响应模型
        return QueryResponseModel(
            success=result.success,
            query=result.query,
            response=result.response,
            tool_calls=result.tool_calls,
            execution_time=result.execution_time,
            error=result.error
        )
        
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_msg = str(e) if str(e) else "未知错误"
        traceback_msg = traceback.format_exc()
        logger.error(f"处理查询时出错: {error_msg}")
        logger.error(f"完整错误堆栈: {traceback_msg}")
        raise HTTPException(
            status_code=500,
            detail=f"查询处理失败: {error_msg}"
        )


if __name__ == "__main__":
    """
    主程序入口
    
    使用方式:
    python http_server.py
    
    环境变量配置:
    - MCP_SERVER_URL: MCP 服务器地址 (默认: http://localhost:8080/sse)
    - OLLAMA_BASE_URL: Ollama 服务器地址 (默认: http://*************:11434/v1)
    - OLLAMA_MODEL: 使用的模型 (默认: qwen2.5:7b)
    - HTTP_HOST: HTTP 服务器绑定地址 (默认: 0.0.0.0)
    - HTTP_PORT: HTTP 服务器端口 (默认: 8000)
    """
    print(f"🚀 启动 MCP HTTP API 服务器...")
    print(f"📡 默认 MCP 服务器: {server_config.default_mcp_server_url}")
    print(f"🤖 Ollama 服务器: {server_config.ollama_base_url}")
    print(f"🧠 使用模型: {server_config.ollama_model}")
    print(f"🌐 HTTP 服务器: http://{server_config.host}:{server_config.port}")
    print(f"📚 API 文档: http://{server_config.host}:{server_config.port}/docs")
    
    uvicorn.run(
        "http_server:app",
        host=server_config.host,
        port=server_config.port,
        reload=True,
        log_level="info"
    )
